"ui";
"use strict";

// AutoJS6 悬浮窗权限检查
if (!floaty.hasPermission()) {
    toast("需要悬浮窗权限");
    floaty.requestPermission();
    threads.start(function() {
        sleep(2000);
        if (!floaty.hasPermission()) {
            toast("请授予悬浮窗权限");
            exit();
        }
    });
}

// 创建悬浮窗
let window = floaty.rawWindow(
    <frame>
        <card cardCornerRadius="8dp" cardElevation="5dp" margin="0">
            <vertical bg="#DD000000" padding="12" w="200">
                <linear gravity="center_vertical" marginBottom="10">
                    <img src="@drawable/ic_play_circle_outline_black_48dp" 
                         w="20" h="20" tint="#FFFFFF" marginRight="5"/>
                    <text text="脚本控制台" textColor="#FFFFFF" textSize="16sp" 
                          textStyle="bold"/>
                </linear>
                
                <horizontal>
                    <vertical flex="1">
                        <button id="startBtn" text="启动" textColor="#FFFFFF" 
                                backgroundTint="#4CAF50" w="*" h="45" margin="2"/>
                        
                        <button id="logBtn" text="日志" textColor="#FFFFFF" 
                                backgroundTint="#2196F3" w="*" h="45" margin="2"/>
                        
                        <button id="hideBtn" text="隐藏" textColor="#FFFFFF" 
                                backgroundTint="#9C27B0" w="*" h="45" margin="2"/>
                        
                        <button id="exitBtn" text="退出" textColor="#FFFFFF" 
                                backgroundTint="#F44336" w="*" h="45" margin="2"/>
                    </vertical>
                </horizontal>
                
                <card cardCornerRadius="4dp" cardBackgroundColor="#33FFFFFF" 
                      marginTop="8" w="*">
                    <text id="statusText" text="● 就绪" textColor="#00FF00" 
                          textSize="12sp" padding="5" gravity="center"/>
                </card>
            </vertical>
        </card>
    </frame>
);

// 设置悬浮窗初始位置
window.setPosition(device.width - 230, 100);
window.setTouchable(true);

// 拖动功能实现
let windowX, windowY;
let downX, downY;
let offsetX, offsetY;

window.statusText.setOnTouchListener(function(view, event) {
    switch (event.getAction()) {
        case event.ACTION_DOWN:
            windowX = window.getX();
            windowY = window.getY();
            downX = event.getRawX();
            downY = event.getRawY();
            offsetX = downX - windowX;
            offsetY = downY - windowY;
            return true;
        case event.ACTION_MOVE:
            window.setPosition(event.getRawX() - offsetX, 
                             event.getRawY() - offsetY);
            return true;
    }
    return true;
});

let scriptThread = null;
let isRunning = false;

// 主脚本函数
function mainScript() {
    console.log("========== 脚本开始执行 ==========");
    
    // 这里替换为你的实际脚本逻辑
    for (let i = 1; i <= 10; i++) {
        if (!isRunning) {
            console.log("脚本被用户停止");
            break;
        }
        console.log("正在执行第 " + i + " 个任务...");
        sleep(1000);
        
        // 更新状态
        ui.run(() => {
            window.statusText.setText("● 进度: " + i + "/10");
        });
    }
    
    console.log("========== 脚本执行完毕 ==========");
}

// 启动/停止按钮
window.startBtn.click(() => {
    if (!isRunning) {
        isRunning = true;
        ui.run(() => {
            window.startBtn.setText("停止");
            window.startBtn.attr("backgroundTint", "#FF5722");
            window.statusText.setText("● 运行中");
            window.statusText.setTextColor(colors.parseColor("#FFFF00"));
        });
        
        scriptThread = threads.start(function() {
            try {
                mainScript();
            } catch(e) {
                console.error("脚本错误: " + e);
                toast("脚本执行出错");
            } finally {
                ui.run(() => {
                    window.startBtn.setText("启动");
                    window.startBtn.attr("backgroundTint", "#4CAF50");
                    window.statusText.setText("● 已停止");
                    window.statusText.setTextColor(colors.parseColor("#00FF00"));
                    isRunning = false;
                });
            }
        });
        
        toast("脚本已启动");
    } else {
        isRunning = false;
        if (scriptThread && scriptThread.isAlive()) {
            scriptThread.interrupt();
        }
        ui.run(() => {
            window.startBtn.setText("启动");
            window.startBtn.attr("backgroundTint", "#4CAF50");
            window.statusText.setText("● 已停止");
            window.statusText.setTextColor(colors.parseColor("#FF0000"));
        });
        toast("脚本已停止");
    }
});

// 查看日志
window.logBtn.click(() => {
    threads.start(function() {
        console.show();
        console.setPosition(0, device.height / 4);
        console.setSize(device.width, device.height / 2);
    });
    toast("日志窗口已打开");
});

// 隐藏悬浮窗
window.hideBtn.click(() => {
    window.setSize(1, 1);
    toast("悬浮窗已最小化，3秒后恢复");
    setTimeout(() => {
        window.setSize(-2, -2);
    }, 3000);
});

// 退出程序
window.exitBtn.click(() => {
    confirm("确认退出?", "将关闭悬浮窗和所有脚本").then(result => {
        if (result) {
            if (scriptThread && scriptThread.isAlive()) {
                scriptThread.interrupt();
            }
            console.hide();
            window.close();
            toast("程序已退出");
            exit();
        }
    });
});

// 保持脚本运行
setInterval(() => {}, 1000);

toast("悬浮窗已启动");
