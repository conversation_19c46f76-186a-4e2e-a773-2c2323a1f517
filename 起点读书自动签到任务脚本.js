"auto"; //系统配置初始化
auto.waitFor();
console.show();
if (auto.service == null) {
  toastLog("请先开启无障碍服务！");
}
let exchanges = true; //是否周末兑换章节卡
let state = false; //是否执行成功状态
const KX = device.width / 1080; //横坐标比例系数
const KY = device.height / 2340; //纵比例系数
const CENTER = "com.qidian.QDReader.ui.activity.QDBrowserActivity";
const adLoadTimeout = 3 * 1000; // 等待广告加载时间，默认3秒
const forceAdTimeout = 30 * 1000; // 新广告布局强制等待时间，默认15秒
//在423行输入你的6位解锁密码
step = 1600 * KY; //无活动窗口1500，有活动窗口1610
//用于起点主页面判断的2个控件
let findview1 = id("ivSearch").depth(13);
let findview2 = id("ivMoreView").depth(13);
let Account;
let leftTime = 10;
//文件路径
var path = "/sdcard/脚本/data/account.txt";
//创建log文件夹
files.ensureDir("/sdcard/脚本/log/");
files.ensureDir("/sdcard/脚本/img/");
files.ensureDir("/sdcard/脚本/data/");
//判断account文件夹是否存在
if (!files.exists(path)) {
  toastLog("account.txt不存在");
  toastLog("请填写用户名 账户 密码");
  files.write("/sdcard/脚本/data/account.txt", "用户名 账户 密码");
  Abnormalexit();
}
//打开文件
var file = open(path);
//读取文件的所有内容
var textfile = file.read();
//释放txt
file.close();
//存储记录初始化
currentTime = new Date();
month = String(currentTime.getMonth() + 1).padStart(2, "0");
days = String(currentTime.getDate()).padStart(2, "0");
hours = String(currentTime.getHours()).padStart(2, "0");
mins = String(currentTime.getMinutes()).padStart(2, "0");
sec = String(currentTime.getSeconds()).padStart(2, "0");
var currentDate =
  currentTime.getFullYear() +
  "." +
  month +
  "." +
  days +
  " " +
  hours +
  ":" +
  mins +
  ":" +
  sec;
currentDate.toString();
console.setGlobalLogConfig({
  file: `/sdcard/脚本/log/${currentTime.getFullYear()}${month}${days}.log`,
  filePattern: "%m%n",
  maxBackupSize: 16,
  maxFileSize: 512 * 1024 /* 384 KB. */,
});
sleep(500);
console.setSize(800 * KX, 500 * KY);
sleep(500);
console.setPosition(0 * KX, 1400 * KY);
//关闭同名脚本
onlyOne();
wakeup();
unlock();
device.setBrightnessMode(0);
device.setBrightness(0);
device.setMusicVolume(0);
//clearrecents();
sleep(1000);
netcheck();
if (!requestScreenCapture()) {
  toastLog("请求截图权限失败");
  Abnormalexit();
}
log("初始化完毕");
log("\n" + "运行日期：" + currentDate);
//启动起点读书开始执行脚本功能
launch("com.qidian.QDReader");
waitForPackage("com.qidian.QDReader");
sleep(3000);
backhome();
text("书架").untilFind();
log("起点读书启动完毕");
console.hide();
sleep(1000);
var btnOk = id("btnOk").findOnce();
if (btnOk != null) {
  btnOk.click();
  sleep(1000);
}
var imgclose = id("imgClose").findOnce();
if (imgclose != null) {
  imgclose.click();
  sleep(1000);
}
accountverify();
// 判断文档是否为空，若为空返回空数组,若不为空按回车分割。
const array = textfile === "" ? [] : textfile.split("\n");
let h = array.length;
switch (h) {
  default:
    toastLog("账号存在异常，退出");
    Abnormalexit();
    break;
  case (h = 1):
    toastLog("执行1个账号签到");
    account1 = array[0].split(" ");
    if (Account == account1[0]) {
      toastLog(account1[0] + "签到开始");
      Signin();
      toastLog(account1[0] + "签到完成");
      home();
      sleep(1000);
      clearrecents();
      sleep(1000);
      engines.stopAllAndToast();
      device.cancelKeepingAwake();
      lock();
    } else {
      toastLog("未识别账号，退出");
      Abnormalexit();
    }
    break;
  case (h = 2):
    toastLog("执行2个账号签到");
    account1 = array[0].split(" ");
    account2 = array[1].split(" ");
    if (Account == account1[0]) {
      toastLog(account1[0] + "签到开始");
      Signin();
      toastLog(account1[0] + "签到完成");
      backhome();
      Switchaccount();
      var youngmode = id("btnEnterTeen").findOne(5000);
      if (youngmode != null) {
        log("点击我知道了");
        click(540, 1479);
      }
      backhome();
      toastLog(account2[0] + "签到开始");
      Signin();
      toastLog(account2[0] + "签到完成");
      home();
      sleep(1000);
      clearrecents();
      sleep(1000);
      engines.stopAllAndToast();
      device.cancelKeepingAwake();
      lock();
    } else if (Account == account2[0]) {
      toastLog(account2[0] + "签到开始");
      Signin();
      toastLog(account2[0] + "签到完成");
      backhome();
      Switchaccount();
      var youngmode = id("btnEnterTeen").findOne(5000);
      if (youngmode != null) {
        log("点击我知道了");
        click(540 * KX, 1479 * KY);
      }
      backhome();
      toastLog(account1[0] + "签到开始");
      Signin();
      toastLog(account1[0] + "签到完成");
      home();
      sleep(1000);
      clearrecents();
      sleep(1000);
      engines.stopAllAndToast();
      device.cancelKeepingAwake();
      lock();
    } else {
      toastLog("未识别账号，退出");
      Abnormalexit();
    }
}
//签到主程序
function Signin() {
  backhome();
  //去签到页
  checkInAndToCenter();
  var signdays = textMatches(/.*已连续签到.*天.*/).findOne(5000);
  var daycount = parseInt(signdays.text().match(/\d+/)[0]);
  if (daycount < 11) {
    toastLog(`连签${daycount}天，进行补签`);
    var signin = descMatches(/.*再连签.*天.*/).findOne(3000);
    if (signin != null) {
      click(signin.bounds().centerX(), signin.bounds().centerY());
      className("android.widget.TextView").text("签到详情").waitFor();
      sleep(1000);
      sml_move(541 * KX, 300 * KY, 541 * KX, 1690 * KY, 200);
      freeReSignin();
      sleep(3000);
    }
  }
 
  //周日兑换奖励
  exchange();
  var signedin = className("android.widget.TextView")
    .text("今日已签到")
    .findOne(1000);
  if (signedin != null) {
    click(signedin.bounds().centerX(), signedin.bounds().centerY());
  }
  //抽奖
  sleep(2000);
  lottery();
  backhome();
  zuorichang();
  playgame();
  Dailytask();
}
//做日常
function zuorichang() {
  toastLog("做日常中");
  Fulicenter();
  AutoRW();
  toastLog("看视频得章节已完成");
  sml_move(540 * KX, 1800 * KY, 540 * KX, 400 * KY, 200);
  sleep(2000);
}
//关闭广告
function closeAD() {
  for (let i = 0; i < 31; i++) {
    var a1 = textMatches(/观看.+|看.*|跳过广告|已获得/).exists();
    var b1 = className("android.view.ViewGroup").exists();
    if (i == 30) {
      log("关闭广告失败退出");
      Abnormalexit();
    }
    if (a1 || b1) {
      log("广告加载完毕，播放中…");
    }
    var a = textMatches(/观看.+|看.*|跳过广告|已获得/).findOnce();
    var b = className("android.view.ViewGroup").findOnce();
    if (a == null && b == null) {
      sleep(1000);
      log("关闭广告重试");
    }
    if (a != null || b != null) {
      closescreen();
      for (let j = 0; j < 22; j++) {
        sleep(1000);
      }
      floaty.closeAll();
      log("广告播放中...");
      autoclose();
      break;
    }
  }
}
//自动任务
function AutoRW() {
  //log("等待进入福利中心…");132,1401,948,1524
  var c1 = text("规则").exists();
  var d1 = text("看视频得奖励").exists();
  if (c1 || d1) {
    log("已进入福利中心…");
  }
  var c = text("规则").findOnce();
  var d = text("看视频得奖励").findOnce();
  while (c == null && d == null) {
    sleep(1000);
    AutoRW();
    break;
  }
  while (c != null || d != null) {
    watchVideos();
    otherVideoTask();
    break;
  }
  sleep(1000);
}
function isAtADWeb() {
  return (
    textMatches(/.*跳过广告.*|.*广告正在加载.*|.*网络不佳.*/).findOne(1300) ||
    currentActivity() != CENTER
  );
}
/*
function _watchVideos_(uiObjectFunc, count) {
  for (let i = 0; i < count; i++) {
    try {
      if(te
      let btn = uiObjectFunc();
      if (btn && btn.text() == "已领取") {
        log(`${count}次视频已完成，跳过`);
        break;
      }
      if (clicks(btn)) {
        log("看视频", i);
        closeAD();
        iKnow(2300) && sleep(300);
      }
    } catch (e) {
      log(e);
    }
  }
}
 
*/
function _watchVideos_(uiObjectFunc, count) {
  for (let i = 0; i < count; i++) {
    try {
      let btn = uiObjectFunc();
 
      // 检测父级文本是否包含"白泽"
      if (btn) {
        let parent = btn.parent();
        while (parent) {
          // 检查当前父级文本
          if (parent.text() && parent.text().includes("白泽")) {
            log("检测到白泽相关任务，跳过视频任务");
            return; // 直接退出函数
          }
          // 检查父级的子元素文本
          let children = parent.children();
          for (let j = 0; j < children.length; j++) {
            let child = children[j];
            if (child.text() && child.text().includes("白泽")) {
              log("检测到白泽相关任务，跳过视频任务");
              return; // 直接退出函数
            }
          }
          // 向上遍历父级
          parent = parent.parent();
        }
      }
 
      if (btn && btn.text() == "已领取") {
        log(`${count}次视频已完成，跳过`);
        break;
      }
      if (clicks(btn)) {
        log("看视频", i);
        closeAD();
        iKnow(2300) && sleep(300);
      }
    } catch (e) {
      log(e);
    }
  }
}
 
function findOne(o, s, c) {
  if (!o || !c) return null;
  return o.findOne(s) || findOne(o.parent(), s, c - 1);
}
//看8次视频
function watchVideos() {
  let btnSel = textMatches(/看视频|已领取/);
  _watchVideos_(
    () => findOne(text("激励视频任务").findOne(5000), btnSel, 3),
    8
  );
  swipe(
    device.width / 2,
    device.height * 0.9,
    device.width / 2,
    device.height * 0.5,
    800
  );
  sleep(200);
  _watchVideos_(
    () => findOne(textContains("额外看3次").findOne(5000), btnSel, 3),
    3
  );
}
//其他视频
function otherVideoTask() {
  log("其他视频任务:");
  _watchVideos_(() => visibleToUser().text("看视频").findOnce(), 5); //冗余设计
}
//检查是否有网络
function netcheck() {
  var r = http.get("www.baidu.com");
  html = r.body.string();
  var reg = new RegExp("百度一下，你就知道");
  if (!reg.test(html)) {
    toastLog("无网络打开GWIFI联网");
    launch("com.gbcom.gwifi");
    waitForPackage("com.gbcom.gwifi");
    log("GWIFI启动完成");
    sleep(12000);
    home();
    sleep(1000);
  }
}
//唤醒屏幕
function wakeup() {
  for (let i = 1; i < 11; i++) {
    if (!device.isScreenOn()) {
      device.wakeUp(); // 唤醒设备
      log("亮屏" + i + "次");
      sleep(1000);
    } else if (i == 10) {
      log("亮屏失败退出");
      exit();
    } else {
      device.keepScreenOn(4800 * 1000); // 保持亮屏
      break;
    }
  }
}
//判断是否有屏幕锁
function isDeviceLocked() {
  importClass(android.app.KeyguardManager);
  importClass(android.content.Context);
  var km = context.getSystemService(Context.KEYGUARD_SERVICE);
  return km.isKeyguardLocked();
}
// 输入密码
function password_input() {
  var password = "******"; //输入你的6位解锁密码
  for (var i = 0; i < password.length; i++) {
    var p = text(password[i].toString()).findOne(1000);
    longClick(p.centerX(), p.centerY());
    sleep(100);
  }
  log("解锁成功");
}
//解锁屏幕
function unlock() {
  for (let l = 1; l < 6; l++) {
    if (isDeviceLocked()) {
      sml_move(540 * KX, 1800 * KY, 600 * KX, 1000 * KY, 500);
      log("滑动屏幕" + l + "次");
      var mima = text("输入密码").findOne(2000);
      if (mima != null) {
        log("输入密码");
        password_input();
        break;
      }
    } else if (l == 5) {
      log("解锁失败退出");
      exit();
    } else {
      log("无需解锁");
      break;
    }
  }
}
//翻倍
function doubleReward(state) {
  let times = 2;
  while (state && times > 0) {
    sleep(600);
    //点击今日奖励翻倍
    let btn = id("btnVideoCheckIn").findOne(2000);
    if (btn) {
      if (btn.findOne(text(".*福利"))) return false;
      toastLog("点击今日奖励翻倍");
      btn.click();
      sleep(600);
      let btnRight = id("btnRight").findOne(1800);
      if (btnRight) {
        btnRight.click();
        log("点击播放");
      }
      closeAD();
      return true;
    } else {
      btn = idContains("btnToCheckIn").findOne(1500);
      if (btn) {
        toastLog("无双倍奖励，去签到页");
        if (!btn.click()) btn.parent().click();
        sleep(3 * 1000);
        return false;
      }
      if (id("browser_title").text("签到").exists()) {
        toastLog("已到签到页");
        return false;
      }
      times--;
      if (!times) {
        toastLog("重试超限，跳过奖励翻倍任务");
        return false;
      }
      back();
    }
  }
}
//签到页
function videoCheckIn(state) {
  let times = 6;
  let btn = null;
  while (times-- > 0 && !id("browser_title").text("签到").exists()) {
    if (!times) {
      toastLog("重试超限,退出");
      return;
    }
    btn = id("btnVideoCheckIn").findOne(1.7 * 1000);
    if (!btn) {
      btn = id("btnCheckIn").findOne(1.7 * 1000);
    }
    if (btn) {
      if (btn.findOne(textContains("奖励"))) {
        back();
        continue;
      }
      toastLog("去签到页");
      btn.click();
      sleep(3 * 1000);
    } else {
      toastLog("找不到按钮，重试");
      back();
      sleep(1000);
    }
  }
  if (!state) {
    sleep(1500);
    btns = text("去翻倍").findOnce();
    if (btns != null) {
      btns.forEach(function (btn) {
        toastLog("去翻倍");
        btn.click();
        closeAD();
      });
    }
  }
  toastLog("查找按钮领点币");
  btn = desc("看视频再领起点币，最高10点").findOne(5 * 1000);
  if (btn) {
    toastLog("点击看视频领点币");
    btn.click();
    closeAD();
  } else {
    toastLog("找不到视频按钮，退出任务");
    return;
  }
}
//抽奖
function lottery() {
  //toastLog("查找抽奖弹窗按钮");
 
  threads.start(() => clicks(textContains("今日已签").findOne(5000)));
 
  let entry = textMatches(
    /.*去抽奖.*|.*抽奖机会\s*(×[1-9])?|看视频可抽奖/
  ).findOne(3000);
  if (!clicks(entry)) {
    log("未发现入口尝试签到详情抽奖");
    let SignDetails = textMatches(/.*再连签\d+天领好礼.*/).findOnce();
    if (clicks(SignDetails)) {
      className("android.widget.TextView").text("签到详情").waitFor();
      sleep(1000);
      sml_move(541 * KX, 300 * KY, 541 * KX, 1690 * KY, 200);
      let entry = textMatches(
        /.*去抽奖.*|.*抽奖机会\s*(×[1-9])?|看视频可抽奖/
      ).findOne(5000);
      if (!clicks(entry)) {
        log("抽奖完成跳过");
        return;
      }
    }
  }
  id("layoutMainView").waitFor();
  sleep(3000);
  for (let i = 0; i < 8; i++) {
    let bt2 = OCRFindText(390 * KX, 1018 * KY, 300 * KX, 120 * KY);
    if (bt2) {
      if (bt2 == "明天再来") {
        toastLog("无抽奖机会，退出1");
        break;
      } else if (bt2 == "抽奖") {
        toastLog("抽奖");
        click(541 * KX, 1075 * KY);
        sleep(8000);
      } else if (bt2 == "看视频抽奖机会+1" || "看视频抽奖喜+1") {
        toastLog("增加抽奖次数");
        click(541 * KX, 1075 * KY);
        closeAD();
        sleep(3000);
      } else {
        toastLog("识别不到抽奖信息，退出2");
        break;
      }
    } else {
      toastLog("识别不到抽奖信息，退出3");
      break;
    }
  }
  sleep(1000);
  back();
}
//只允许有一个同名脚本运行
function onlyOne() {
  let g = engines.myEngine();
  var e = engines.all(),
    n = e.length;
  let r = g.getSource() + "";
  1 < n &&
    e.forEach((e) => {
      var n = e.getSource() + "";
      g.id !== e.id && n == r && e.forceStop();
    });
}
//清理后台
function clearrecents() {
  recents();
  sleep(1000);
  var clearbox = id("clearbox").findOne(1000);
  if (clearbox != null) {
    click(clearbox.bounds().centerX(), clearbox.bounds().centerY());
    sleep(1000);
    home();
  } else {
    home();
  }
}
function lock() {
  var success = runtime.accessibilityBridge
    .getService()
    .performGlobalAction(
      android.accessibilityservice.AccessibilityService
        .GLOBAL_ACTION_LOCK_SCREEN
    );
}
function closescreen() {
  var w = floaty.rawWindow(<frame gravity="center" bg="#000000" />);
  w.setSize(device.width, device.height);
  w.setPosition(0, -105 * KY);
  w.setTouchable(false);
  //保持脚本运行
  setInterval(() => {}, 1000);
}
//每月福利
function monthfuli() {
  //log("等待进入福利广场");
  var f1 = text("规则").exists();
  var g1 = text("看视频得奖励").exists();
  var f = text("规则").findOnce();
  var g = text("看视频得奖励").findOnce();
  while (f == null && g == null) {
    sleep(500);
    log("正在进入福利中心");
    monthfuli();
    break;
  }
  while (g != null) {
    var kanship = text("看视频").findOnce();
    if (kanship != null) {
      click(kanship.bounds().centerX(), kanship.bounds().centerY());
      closeAD();
      var happyget = text("知道了").findOne(5000);
      if (happyget != null) {
        toastLog("自动点击");
        click(happyget.bounds().centerX(), happyget.bounds().centerY());
        backhome();
        sleep(2000);
        EnterFuli();
      }
    } else {
      break;
    }
  }
}
//仿真曲线滑动
function bezier_curves(cp, t) {
  cx = 3.0 * (cp[1].x - cp[0].x);
  bx = 3.0 * (cp[2].x - cp[1].x) - cx;
  ax = cp[3].x - cp[0].x - cx - bx;
  cy = 3.0 * (cp[1].y - cp[0].y);
  by = 3.0 * (cp[2].y - cp[1].y) - cy;
  ay = cp[3].y - cp[0].y - cy - by;
  tSquared = t * t;
  tCubed = tSquared * t;
  result = {
    x: 0,
    y: 0,
  };
  result.x = ax * tCubed + bx * tSquared + cx * t + cp[0].x;
  result.y = ay * tCubed + by * tSquared + cy * t + cp[0].y;
  return result;
}
//仿真随机带曲线滑动
//qx, qy, zx, zy, time 代表起点x,起点y,终点x,终点y,过程耗时单位毫秒
function sml_move(qx, qy, zx, zy, time) {
  var xxy = [time];
  var point = [];
  var dx0 = {
    x: qx,
    y: qy,
  };
  var dx1 = {
    x: random(qx - 100, qx + 100),
    y: random(qy, qy + 50),
  };
  var dx2 = {
    x: random(zx - 100, zx + 100),
    y: random(zy, zy + 50),
  };
  var dx3 = {
    x: zx,
    y: zy,
  };
  for (var i = 0; i < 4; i++) {
    eval("point.push(dx" + i + ")");
  }
  //log(point[3].x)
  for (let i = 0; i < 1; i += 0.08) {
    xxyy = [
      parseInt(bezier_curves(point, i).x),
      parseInt(bezier_curves(point, i).y),
    ];
    xxy.push(xxyy);
  }
  //log(xxy);
  gesture.apply(null, xxy);
}
//返回主界面
function backhome() {
  log("检测是否在主界面");
  for (let i = 1; i < 21; i++) {
    //bounds(792, 114, 912, 234)
    //bounds(936, 111, 1056, 231)
    var homepage = findview1.findOnce() && findview2.findOnce();
    if (homepage != null) {
      log("已在主界面");
      break;
    } else if (i == 20) {
      toastLog("返回主界面失败退出，请检查控件ivSearch及ivMoreView的bounds");
      Abnormalexit();
      break;
    } else {
      back();
      log("正在返回主界面中...");
      sleep(2000);
    }
  }
}
//广告自动关闭
function autoclose() {
  let tipsSel = visibleToUser().textMatches(/.*\d+[sS秒].*|.*(领取|获得).*/);
  let maxRetry = 15,
    closeBtn = null;
  let x = device.width * 0.85,
    y = device.height * 0.16;
 
  do {
    let c = 52;
    let tips = tipsSel.findOne(3000);
    while (c-- && tips && /^(?!.*(成功|已)).*|.*完.*/.test(tips.text())) {
      sleep(1000);
      tips = tipsSel.findOnce();
    }
 
    let isNewLayout = classNameContains("ViewGroup").find().length > 30;
    if (isNewLayout) {
      log("新布局,强制等待%d秒", forceAdTimeout / 1000);
      sleep(forceAdTimeout);
      let btns = visibleToUser()
        .classNameContains("ViewGroup")
        .filter((o) => o.bounds().bottom < y && o.bounds().left > x)
        .find();
      closeBtn = btns.reduce(
        (pre, cur) => (pre.bounds().left > cur.bounds().left ? pre : cur),
        btns[0]
      );
    } else {
      closeBtn =
        textContains("跳过").findOne(1200) ||
        bounds(948, 217, 1044, 313).findOne(1200) ||
        visibleToUser()
          .className("ImageView")
          .filter((o) => Math.max(o.bounds().bottom, o.bounds().right) < y)
          .findOnce() ||
        visibleToUser()
          .className("LinearLayout")
          .clickable()
          .filter((o) => o.bounds().bottom < y && o.bounds().left > x)
          .findOne(1e3);
    }
 
    clicks(closeBtn) && log("关闭广告：", closeBtn.bounds());
  } while (maxRetry-- && clicks(text("继续观看").findOne(1500)));
}
/*
  for (let i = 1; i < 11; i++) {
    //bounds(972, 241, 1020, 289)
    //bounds(60, 150, 150, 240)
    //bounds(966, 189, 1002, 225
    var closead1 = text("跳过广告").findOnce();
    var closead2 = text("| 跳过").findOnce();
    var closead3 = bounds(60 * KX, 150 * KY, 150 * KX, 240 * KY).findOnce();
    var closead4 = bounds(966 * KX, 189 * KY, 1002 * KX, 225 * KY).findOnce();
    var closead5 = bounds(33 * KX, 171 * KY, 126 * KX, 264 * KY).findOnce();
    if (
      closead1 != null ||
      closead2 != null ||
      closead3 != null ||
      closead4 != null ||
      closead5 != null
    ) {
      //全不为空
      if (closead1 != null) {
        log("点击1退出按钮" + i + "次");
        click(closead1.bounds().centerX(), closead1.bounds().centerY());
        sleep(1000);
      } else if (closead2 != null) {
        log("点击2退出按钮" + i + "次");
        click(closead2.bounds().centerX(), closead2.bounds().centerY());
        sleep(1000);
      } else if (closead3 != null) {
        log("点击3退出按钮" + i + "次");
        click(closead3.bounds().centerX(), closead3.bounds().centerY());
        sleep(1000);
      } else if (closead4 != null) {
        log("点击4退出按钮" + i + "次");
        click(closead4.bounds().centerX(), closead4.bounds().centerY());
        sleep(1000);
      } else {
        log("点击5退出按钮" + i + "次");
        click(closead5.bounds().centerX(), closead5.bounds().centerY());
        sleep(1000);
      }
    } else if (i == 10) {
      log("关闭广告失败退出");
      Abnormalexit();
      break;
    }
    var signal1 = textMatches(/.*福利.).findOnce();
    var signal2 = textMatches(/.*签到.).findOnce();
    var signal3 = textMatches(/.*知道了.).findOnce();
    if (signal1 != null || signal2 != null || signal3 != null) {
      break;
    } else {
      log("广告未关闭重试");
      sleep(500);
    }
    var keepview = className("android.widget.TextView")
      .text("继续观看")
      .findOnce() || className('android.view.View').bounds(225, 1362, 855, 1427).findOnce();
    if (keepview != null) {
      click(keepview.bounds().centerX(), keepview.bounds().centerY());
      closescreen();
      sleep(5000);
      floaty.closeAll();
      log("保持观看点击退出");
    }
  }
}
*/
//自动完成阅读任务
function Aread() {
  let j = 0;
  sleep(1000);
  for (let i = 1; i < 7; i++) {
    text("任务书单").untilFind();
    var reading = className("android.widget.Button").text("阅读").findOne(5000);
    if (reading != null) {
      click(reading.centerX(), reading.centerY() + j);
      sleep(3000);
      log("第" + i + "次阅读开始");
      for (let n = 0; n < 6; n++) {
        sml_move(900 * KX, 1334 * KY, 300 * KX, 1340 * KY, 200);
        sleep(500);
        if (n == 0) {
          click(543 * KX, 1230 * KY);
          sleep(1000);
          click(146 * KX, 2217 * KY);
          sleep(1000);
          for (let m = 0; m < 7; m++) {
            sml_move(540 * KX, 800 * KY, 540 * KX, 1900 * KY, 200);
          }
          sleep(1000);
          click(550 * KX, 476 * KY);
          var quxiao = text("取消").findOne(1000);
          if (quxiao != null) {
            click(quxiao.bounds().centerX(), quxiao.bounds().centerY());
          }
        }
        closescreen();
        sleep(20000);
        floaty.closeAll();
        sleep(500);
      }
      log("第" + i + "次阅读结束");
      back();
      sleep(500);
      var quxiao = text("取消").findOne(2000);
      if (quxiao != null) {
        click(quxiao.bounds().centerX(), quxiao.bounds().centerY());
      }
      j += 246;
    }
  }
  backhome();
  sleep(1000);
  EnterFuli();
  sml_move(540 * KX, step * KY, 540 * KX, 900 * KY, 200);
  sleep(5000);
}
//自动完成听书任务
function Alisten() {
  sleep(3000);
  var books = textEndsWith("万字").findOne(3000);
  if (books != null) {
    click(books.bounds().centerX(), books.bounds().centerY());
    sleep(3000);
    var tingbook = id("tvAiAUdio").findOne(1000);
    if (tingbook != null) {
      click(tingbook.bounds().centerX(), tingbook.bounds().centerY());
      sleep(3000);
    }
  } else {
    click(555 * KX, 1110 * KY);
    sleep(3000);
    var tingbook = id("tvAiAUdio").findOne(1000);
    if (tingbook != null) {
      click(tingbook.bounds().centerX(), tingbook.bounds().centerY());
      sleep(3000);
    }
  }
  for (let i = 1; i < 5; i++) {
    var play = id("ivAddBook").findOne(5000);
    if (play != null) {
      var download = id("btnRight").findOne(2000);
      if (download != null) {
        log("等待下载语音包");
        sleep(15000);
      }
    } else {
      var download = id("btnRight").findOne(2000);
      if (download != null) {
        log("等待下载语音包");
        sleep(15000);
      }
    }
    sleep(2000);
    Imagecompare();
    if (p) {
      closescreen();
      log("听书开始");
      for (let j = 0; j < 61; j++) {
        sleep(1000);
      }
      log("听书结束");
      floaty.closeAll();
      back();
      var canceljoin = id("button_text_id")
        .className("android.widget.TextView")
        .text("取消")
        .findOne(3000);
      if (canceljoin != null) {
        click(canceljoin.bounds().centerX(), canceljoin.bounds().centerY());
      }
      sleep(500);
      backhome();
      id("ivClose").findOne().click();
      sleep(2000);
      EnterFuli();
      break;
    } else {
      log("未播放重试");
      click(543 * KX, 1949 * KY);
      sleep(2000);
    }
  }
}
//每日任务
function Dailytask() {
  for (let u = 0; u < 8; u++) {
    sleep(1000);
    var tofinish = text("去完成").findOnce(u);
    if (tofinish != null) {
      click(tofinish.bounds().centerX(), tofinish.bounds().centerY());
      sleep(2000);
      var tingshu = id("mTitleTextView").text("听原创小说").findOnce();
      var gamecenter = id("browser_title").text("游戏中心").findOnce();
      var huodong = className("android.widget.TextView")
        .text("分享任务")
        .findOnce();
      var cartoon = id("search")
        .bounds(972 * KX, 141 * KY, 1044 * KX, 213 * KY)
        .findOnce();
      var mainpage = findview1.findOnce() && findview2.findOnce();
      var browseactivities = text("去阅读").findOnce();
      if (browseactivities != null) {
        log("执行浏览任务");
        sleep(5000);
        back();
        sleep(2000);
        EnterFuli();
      }
      if (mainpage != null) {
        log("跳过订阅任务");
        sleep(1000);
        EnterFuli();
      } else if (tingshu != null) {
        log("执行听书任务");
        Alisten();
        u = u - 1;
      } else if (gamecenter != null) {
        log("跳过充值任务");
        back();
        sleep(1000);
      } else if (cartoon != null) {
        log("执行漫画任务");
        Acartoon();
        u = u - 1;
      } else if (huodong != null) {
        log("跳过活动任务");
        back();
        sleep(1000);
      } else {
        log("跳过任务");
        sleep(2000);
        home();
        sleep(1000);
        launch("com.qidian.QDReader");
        waitForPackage("com.qidian.QDReader");
        sleep(3000);
        backhome();
        EnterFuli();
      }
    }
  }
 
  getjl();
}
//获取奖励
function getjl() {
  let j = 0;
  for (let i = 0; i < 6; i++) {
    var LJL = text("领奖励").findOnce(i);
    if (LJL != null) {
      click(LJL.bounds().centerX(), LJL.bounds().centerY());
      var get = text("知道了").findOne(5000);
      if (get != null) {
        click(get.bounds().centerX(), get.bounds().centerY());
        i = i - 1;
        j++;
        log("已领取奖励" + j + "次");
        sleep(2000);
      }
    } else {
      log("已无奖励退出");
      break;
    }
  }
}
function Fulicenter() {
  sleep(1000);
  var dianjiwo = id("view_tab_title_title")
    .className("android.widget.TextView")
    .text("我")
    .findOne(1000);
  if (dianjiwo != null) {
    click(dianjiwo.bounds().centerX(), dianjiwo.bounds().centerY());
  }
  sleep(1000);
  var fulizx = text("福利中心").findOne(5000);
  if (fulizx != null) {
    click(fulizx.bounds().centerX(), fulizx.bounds().centerY());
  }
  sleep(3000);
  var tiaoguo = text("跳过教程").findOne(1000);
  if (tiaoguo != null) {
    click(tiaoguo.bounds().centerX(), tiaoguo.bounds().centerY());
  }
  var openbox = text("1次免费开宝箱机会").findOnce(5000);
  if (openbox != null) {
    clickButton(waitView("立即开启"));
    clickButton(waitView("看视频再领取一次"));
    closeAD();
    autoclose();
    sleep(3000);
  }
  text("看视频得奖励").untilFind;
  sleep(3000);
}
//进入福利中心
function EnterFuli() {
  sleep(1000);
  var dianjiwo = id("view_tab_title_title")
    .className("android.widget.TextView")
    .text("我")
    .findOne(1000);
  if (dianjiwo != null) {
    click(dianjiwo.bounds().centerX(), dianjiwo.bounds().centerY());
  }
  sleep(1000);
  var fulizx = text("福利中心").findOne(5000);
  if (fulizx != null) {
    click(fulizx.bounds().centerX(), fulizx.bounds().centerY());
  }
  sleep(3000);
  var tiaoguo = text("跳过教程").findOne(1000);
  if (tiaoguo != null) {
    click(tiaoguo.bounds().centerX(), tiaoguo.bounds().centerY());
  }
  text("每日福利").untilFind;
  var unfold = className("android.widget.TextView").text("展开").findOne(1000);
  if (unfold != null) {
    click(unfold.bounds().centerX(), unfold.bounds().centerY());
  }
  sleep(3000);
  sml_move(540 * KX, 1800 * KY, 540 * KX, 400 * KY, 200);
  sleep(5000);
}
//图片比对
function Imagecompare() {
  //captureScreen("/sdcard/脚本/Listen.png");
  //var rawimg = images.read("/sdcard/脚本/Listen.png");
  //log("截图成功");
  //ROI区域(x,y,宽，高)
  //var pauseorplay = images.clip(rawimg,438,1848,204,204);
  //images.save(pauseorplay, "/sdcard/脚本/pauseorplay.png");
  var img_small = images.read("/sdcard/脚本/img/pauseorplay.png");
  //找图
  //在大图片中查找小图片的位置（模块匹配），找到时返回位置坐标(Point),找不到时返回null。
  var img = captureScreen();
  //images.save(img, "/sdcard/脚本/Listen.png");
  //var pauseorplay = images.clip(img,450,1776,174,174);
  //images.save(pauseorplay, "/sdcard/脚本/pauseorplay.png");
  p = findImage(img, img_small, { threshold: 0.6 });
  if (p && p.length > 0) {
    p = null;
  }
  img_small.recycle();
  img_small = null;
  img.recycle();
  img = null;
}
//看漫画任务
function Acartoon() {
  //bounds(564, 1220, 1032, 1688)
  //(564, 1220, 1032, 1688)
  id("search").untilFind();
  sleep(3000);
  click(806 * KX, 1447 * KY);
  sleep(2000);
  var lookcartoon = text("立即阅读").findOne(3000);
  if (lookcartoon != null) {
    click(lookcartoon.bounds().centerX(), lookcartoon.bounds().centerY());
    sleep(1000);
    for (let i = 1; i < 16; i++) {
      className("android.view.View").untilFind();
      log("第" + i + "次阅读开始");
      closescreen();
      for (let j = 0; j < 20; j++) {
        sleep(1000);
      }
      floaty.closeAll();
      sleep(500);
      log("第" + i + "次阅读结束");
      sml_move(540 * KX, 1500 * KY, 540 * KX, 900 * KY, 200);
      sleep(500);
    }
    back();
    sleep(500);
    var buyong = text("不用了").findOne(2000);
    if (buyong != null) {
      click(buyong.bounds().centerX(), buyong.bounds().centerY());
    }
    backhome();
    sleep(1000);
    EnterFuli();
  }
}
//周末兑换章节卡
function exchange() {
  log("兑换章节卡");
  if (!exchanges || new Date().getDay()) {
    log("非周日或者设置不领取");
    return;
  }
 
  threads.start(() => clicks(text("今日已签到").findOne(2000)));
  if (!clicks(textMatches(/去?兑换\s*(周日)?|.*积攒碎片兑.*/).findOne(4000)))
    return;
 
  let x = device.width * 0.75,
    y = device.height * 0.25;
  let tips = textMatches(/\s*\d+\s*张碎片可兑换/).findOne(2000);
  let n = tips ? parseInt(tips.text().match(/\d+/)[0]) : 0;
  log("%d张碎片可兑换", n);
  switch (true) {
    case n < 15:
      log("碎片不够15张，结束兑换");
      click(x, y);
      return;
    case n < 20:
      n = 15;
      log("兑换10点章节卡");
      break;
    case n < 30:
      n = 20;
      log("兑换15点章节卡");
      break;
    case n >= 30:
      n = 30;
      log("兑换20点章节卡");
  }
 
  clicks(findOne(text(n + "张碎片兑换").findOne(2000), text("兑换"), 2));
  sleep(700);
  clicks(className("Button").text("兑换").findOne(3000));
  sleep(3500);
 
  if (textMatches(/.*验证.*/).findOne(2000)) {
    console.error("有验证，兑换失败");
    clicks(textContains("关闭验证").findOne(2000)) || click(x, y);
    sleep(1000);
  }
  click(x, y);
  click(x, y);
  backhome();
  checkInAndToCenter();
}
function freeReSignin() {
  let signindays = textMatches(/当前已连续签到.*/).findOne(4000);
  if (signindays != null) {
    toastLog(signindays.text());
  }
  sml_move(541 * KX, 300 * KY, 541 * KX, 1690 * KY, 1000);
  sleep(5000);
  log("查找免费补签");
  //bounds(36, 558, 1044, 1806)
  for (j = 0; j < 7; j++) {
    let txt = OCRFindCoord(36 * KX, 558 * KY, 1008 * KX, 1248 * KY, [
      "免费补签",
      "去补签",
    ]);
    if (txt && txt != null) {
      log("有未补签，进行补签");
      sleep(600);
      click(txt[0], txt[1]);
      sleep(2000);
      for (i = 0; i < 15; i++) {
        FreeButton = OCRFindCoord(180 * KX, 986 * KY, 720 * KX, 540 * KY, [
          "去看视频",
          "99点补签",
        ]);
        if (i == 14) {
          log("未能识别到关键字退出");
          Abnormalexit();
        } else if (FreeButton[2] == "去看视频") {
          click(FreeButton[0], FreeButton[1]);
          closeAD();
          autoclose();
          toastLog("补签完成");
          sleep(2000);
          break;
        } else if (FreeButton[2] == "99点补签") {
          click(FreeButton[0], FreeButton[1]);
          toastLog("补签完成");
          sleep(2000);
          break;
        } else {
          log("未识别重试");
          sleep(1000);
        }
      }
    } else {
      toastLog("无需补签");
      break;
    }
  }
}
 
function iKnow(t) {
  return clicks(
    visibleToUser()
      .text("知道了")
      .findOne(t || 2500),
    0
  );
}
 
function playgame() {
  // 玩游戏
  let c = 4;
  while (c--) {
    try {
      let gametxt = textMatches(/当日玩游戏\d+分钟/).findOnce();
      let btn = findOne(gametxt, textMatches(/领奖励|去完成|已领取/), 3);
      if (btn == null || btn.text() == "已领取") return;
      if (btn.text() == "领奖励") {
        clicks(btn);
        iKnow(3000);
        return;
      }
      if (btn.text() == "去完成") {
        let leftTimeTips = findOne(gametxt, textContains("再玩"), 3);
        leftTime = parseInt(leftTimeTips.text().match(/\d+/)[0]);
        log("还需玩" + leftTime + "分钟");
        clicks(btn, 1);
        var gamead = id("browser_title").text("游戏中心").findOne(5000);
        if (gamead != null) {
          clickButton(waitView("新游"));
          sleep(5000);
          clickButton(waitView("在线玩"));
          id("qd_recycler_view").waitFor();
        }
        closescreen();
        //较长延时需用多重for循环，避免时间不精确
        for (let i = 0; i < leftTime + 1; i++) {
          for (let j = 0; j < 60; j++) {
            sleep(1000);
          }
        }
        floaty.closeAll();
        log("游戏挂机结束");
        recents();
        sleep(1000);
        sml_move(1000 * KX, 1018 * KY, 50 * KX, 1018 * KY, 600);
        sleep(3000);
        sml_move(541 * KX, 1680 * KY, 541 * KX, 300 * KY, 300);
        sleep(2000);
        launch("com.qidian.QDReader");
        waitForPackage("com.qidian.QDReader");
        sleep(3000);
        backhome();
        EnterFuli();
      }
    } catch (e) {
      log(e);
    }
  }
}
/**
 * 根据文字查找按钮并点击
 * @param {UiObject} view 按钮上的文字所在 view
 * @returns 是否成功点击
 */
function clickButton(view) {
  if (view.text != null) {
    log("点击控件" + view.text());
  } else if (view.desc != null) {
    log("点击控件" + view.desc());
  }
  // 查找按钮所在控件
  let btn = view;
  while (btn && !btn.clickable()) {
    btn = btn.parent();
  }
  // 点击
  if (btn) {
    btn.click();
    return true;
  }
  return false;
}
/**
 * 查找带有某个文本的控件
 * home.php?mod=space&uid=952169 {string} content 查找文本
 * @param {string} mode 查找方式，默认 text，可选 match
 * @returns 第一个符合条件的控件，不存在返回 undefined
 */
function findView(content, mode) {
  log(`查找控件 ${content}`);
  let find;
  if (mode === "match") {
    find = textMatches(content) || descMatches(content);
  } else {
    find = text(content) || desc(content);
  }
  return find && find.exists() ? find.findOnce() : undefined;
}
/**
 * 查找带有某个文本的控件
 * @param {string} content 查找文本
 * @returns 第一个符合条件的控件
 */
function waitView(content) {
  log(`等待控件 ${content}`);
  let view = text(content) || desc(content);
  view.waitFor();
  return view.findOnce();
}
function Switchaccount() {
  var dianjiwo = id("view_tab_title_title")
    .className("android.widget.TextView")
    .text("我")
    .findOne(1000);
  if (dianjiwo != null) {
    click(dianjiwo.bounds().centerX(), dianjiwo.bounds().centerY());
  }
  sleep(1000);
  text("福利中心").untilFind;
  id("ivSetting").findOne().click();
  waitView("设置");
  sml_move(540 * KX, step * KY, 540 * KX, 900 * KY, 400);
  clickButton(waitView("切换账号"));
  id("privacyView").waitFor();
  click(96 * KX, 2143 * KY);
  sleep(1000);
  //bounds(646, 1941, 778, 2073)
  click(772 * KX, 2007 * KY);
  waitView("起点账号登录");
  if (Account == account1[0]) {
    log("切换账号" + account2[0]);
    Accountname = id("mNickNameEditText").findOne();
    Accountname.setText("");
    sleep(500);
    Accountname.setText(account2[1]);
    sleep(1000);
    back(); //避免自动填充影响输入
    sleep(1000);
    Accountpassword = id("mPwdEditText").findOne();
    Accountpassword.setText("");
    sleep(500);
    Accountpassword.setText(account2[2]);
    sleep(1000);
    clickButton(waitView("登录"));
    if (textMatches(/.*验证.*/).findOne(2000)) {
      console.error("有验证，登陆失败");
      clicks(textContains("关闭").findOne(1000));
      sleep(500);
      Abnormalexit();
    }
    var updatepassword = text("更新").findOne(5000); //更新自动填充
    if (updatepassword != null) {
      sleep(500);
      click(
        updatepassword.bounds().centerX(),
        updatepassword.bounds().centerY()
      );
    }
    waitView(account2[0]);
    log(account2[0] + "登录成功");
  } else if (Account == account2[0]) {
    log("切换账号" + account1[0]);
    Accountname = id("mNickNameEditText").findOne();
    Accountname.setText("");
    sleep(500);
    Accountname.setText(account1[1]);
    sleep(1000);
    back(); //避免自动填充影响输入
    sleep(1000);
    Accountpassword = id("mPwdEditText").findOne();
    Accountpassword.setText("");
    sleep(500);
    Accountpassword.setText(account1[2]);
    sleep(1000);
    clickButton(waitView("登录"));
    if (textMatches(/.*验证.*/).findOne(2000)) {
      console.error("有验证，登陆失败");
      clicks(textContains("关闭").findOne(1000));
      sleep(500);
      Abnormalexit();
    }
    var updatepassword = text("更新").findOne(5000); //更新自动填充
    if (updatepassword != null) {
      sleep(500);
      click(
        updatepassword.bounds().centerX(),
        updatepassword.bounds().centerY()
      );
    }
    waitView(account1[0]);
    log(account1[0] + "登录成功");
  } else {
    log("未识别到账号，退出");
    Abnormalexit();
  }
}
function accountverify() {
  var dianjiwo = id("view_tab_title_title")
    .className("android.widget.TextView")
    .text("我")
    .findOne(1000);
  if (dianjiwo != null) {
    click(dianjiwo.bounds().centerX(), dianjiwo.bounds().centerY());
  }
  var youngmode = id("btnEnterTeen").findOne(5000);
  if (youngmode != null) {
    log("点击我知道了");
    click(540 * KX, 1479 * KY);
  }
  var btnclose = id("btnClose").findOnce();
  if (btnclose != null) {
    btnclose.click();
    sleep(1000);
  }
  text("福利中心").untilFind;
  Account = id("tvName").findOne().text();
  backhome();
}
//查询子控件
function clicks(o, t) {
  let p = o,
    r = false;
  t = t || 1;
  if (!o) return r;
  while (t-- && !(r = p.clickable())) p = p.parent();
  return p.click() || click(o.bounds().centerX(), o.bounds().centerY());
}
//文本截取
function textcut(str, firstStr, secondStr) {
  if (firstStr == "") {
    str = "**" + str;
    firstStr = "**";
  }
  if (secondStr == "") {
    str = str + "**";
    secondStr = "**";
  }
  if (str == "" || str == null || str == undefined) {
    return "";
  }
  if (str.indexOf(firstStr) < 0) {
    return "";
  }
  var subFirstStr = str.substring(
    str.indexOf(firstStr) + firstStr.length,
    str.length
  );
  var subSecondStr = subFirstStr.substring(0, subFirstStr.indexOf(secondStr));
  return subSecondStr;
}
//OCR文字识别
function OCRFindText(x, y, w, h) {
  let rawimg = captureScreen();
  //log("截图成功");
  //ROI区域(x,y,宽，高)
  let img = images.clip(rawimg, x, y, w, h);
  //images.save(img, "/sdcard/脚本/ocr.png");
  const Predictor = com.baidu.paddle.lite.ocr.Predictor;
  // 指定是否用精简版模型 速度较快
  let useSlim = false;
  // 创建检测器
  let predictor = new Predictor();
  // predictor.cpuThreadNum = 4 //可以自定义使用CPU的线程数
  // predictor.checkModelLoaded = false // 可以自定义是否需要校验模型是否成功加载 默认开启 使用内置Base64图片进行校验 识别测试文本来校验模型是否加载成功
  // 初始化模型 首次运行时会比较耗时
  let loading = threads.disposable();
  // 建议在新线程中初始化模型
  threads.start(function () {
    loading.setAndNotify(predictor.init(context, useSlim));
    // loading.setAndNotify(predictor.init(context)) 为默认不使用精简版
    // 内置默认 modelPath 为 models/ocr_v3_for_cpu，初始化自定义模型请写绝对路径否则无法获取到
    // 内置默认 labelPath 为 labels/ppocr_keys_v1.txt
    // let modelPath = files.path('./models/customize') // 指定自定义模型路径
    // let labelPath = files.path('./models/customize') // 指定自定义label路径
    // 使用自定义模型时det rec cls三个模型文件名称需要手动指定
    // predictor.detModelFilename = 'det_opt.nb'
    // predictor.recModelFilename = 'rec_opt.nb'
    // predictor.clsModelFilename = 'cls_opt.nb'
    // loading.setAndNotify(predictor.init(context, modelPath, labelPath))
  });
  let loadSuccess = loading.blockedGet();
  //log(`加载模型结果：${loadSuccess}`);
  let start = new Date();
  let results = predictor.runOcr(img.getBitmap());
  let OcrResult = null;
  if (results && results.length > 0) {
    //{"label":"看视频抽奖喜＋1"}]
    OcrResult = results[0].label;
  }
  // 回收图片
  rawimg.recycle();
  rawimg = null;
  img.recycle();
  img = null;
  //log(OcrResult);
  return OcrResult;
  // 释放模型 用于释放native内存 非必需
  // predictor.releaseModel()
}
function Abnormalexit() {
  console.hide();
  let img = captureScreen();
  images.save(img, "/sdcard/脚本/img/SigninAbnormal.png");
  img.recycle();
  home();
  sleep(1000);
  clearrecents();
  sleep(1000);
  engines.stopAllAndToast();
  device.cancelKeepingAwake();
  lock();
}
//找字
function OCRFindCoord(x, y, w, h, word) {
  let rawimg = captureScreen();
  //ROI区域(x,y,宽，高)
  let img = images.clip(rawimg, x, y, w, h);
  //images.save(img, "/sdcard/脚本/ocr.png");
  const Predictor = com.baidu.paddle.lite.ocr.Predictor;
  // 指定是否用精简版模型 速度较快
  let useSlim = false;
  // 创建检测器
  let predictor = new Predictor();
  // predictor.cpuThreadNum = 4 //可以自定义使用CPU的线程数
  // predictor.checkModelLoaded = false // 可以自定义是否需要校验模型是否成功加载 默认开启 使用内置Base64图片进行校验 识别测试文本来校验模型是否加载成功
  // 初始化模型 首次运行时会比较耗时
  let loading = threads.disposable();
  // 建议在新线程中初始化模型
  threads.start(function () {
    loading.setAndNotify(predictor.init(context, useSlim));
    // loading.setAndNotify(predictor.init(context)) 为默认不使用精简版
    // 内置默认 modelPath 为 models/ocr_v3_for_cpu，初始化自定义模型请写绝对路径否则无法获取到
    // 内置默认 labelPath 为 labels/ppocr_keys_v1.txt
    // let modelPath = files.path('./models/customize') // 指定自定义模型路径
    // let labelPath = files.path('./models/customize') // 指定自定义label路径
    // 使用自定义模型时det rec cls三个模型文件名称需要手动指定
    // predictor.detModelFilename = 'det_opt.nb'
    // predictor.recModelFilename = 'rec_opt.nb'
    // predictor.clsModelFilename = 'cls_opt.nb'
    // loading.setAndNotify(predictor.init(context, modelPath, labelPath))
  });
  let loadSuccess = loading.blockedGet();
  //log(`加载模型结果：${loadSuccess}`);
  let start = new Date();
  let results = predictor.runOcr(img.getBitmap());
  let OcrCoord = null;
  if (results && results.length > 0) {
    for (let u = 0; u < word.length; u++) {
      for (let v = 0; v < results.length; v++) {
        if (word[u] == results[v].label) {
          button = results[v].bounds;
          OcrCoord = [
            Math.floor(x + button.left + (button.right - button.left) / 2),
            Math.floor(y + button.top + (button.bottom - button.top) / 2),
            word[u],
          ];
          rawimg.recycle();
          rawimg = null;
          img.recycle();
          img = null;
          results = null;
          button = null;
          return OcrCoord;
        }
      }
    }
  }
  //log(OcrResult);
  // 释放模型 用于释放native内存 非必需
  // predictor.releaseModel()
  // 回收图片
  rawimg.recycle();
  img.recycle();
}
//检查是否签到和进入福利中心
function checkInAndToCenter() {
  let selector1 = id("btnCheckIn");
  let selector2 = text("做任务领章节卡");
  let btn = selector1.findOne(5000);
  if (clicks(btn)) {
    let txt = btn.findOne(id("button_text_id"));
    if (txt.text() == "领福利") {
      log("已签到过了，去签到页");
    } else {
      closeDialog();
      clicks(selector1.findOne(2000));
    }
  } else {
    clicks(selector2.findOne(5000));
  }
  let c = 3;
  while (c-- && !textMatches(/.*看视频*|.*已连续签到.*天.*/).findOne(6e3)) {
    log("未加载数据，返回重试");
    back();
    clicks(selector1.findOne(2000) || selector2.findOnce());
  }
}
 
function closeDialog() {
  threads.start(function () {
    clicks(idMatches(/.*[cC]lose.*/).findOne(5000));
  });
}