// 业务处理示例脚本
// 演示如何读取大厅订单配置并进行业务处理

// 导入配置模块（如果需要的话）
// var configModule = require('./大厅订单界面.js');

// 直接读取配置存储
var storage = storages.create('hall_order_config');

// 默认配置
function getDefaultConfig() {
    return {
        hallOrderEnabled: true,
        orderTypes: ['快车', '特惠快车'],
        amountEnabled: false,
        minAmount: 0,
        maxAmount: 999,
        startDistanceEnabled: false,
        maxStartDistance: 10,
        totalDistanceEnabled: false,
        minTotalDistance: 5,
        unitPriceEnabled: false,
        minUnitPrice: 2.0,
        bigOrderEnabled: false,
        bigOrderAmount: 100,
        refreshIntervalMin: 500,
        refreshIntervalMax: 1000
    };
}

// 读取配置
function getConfig() {
    return storage.get('config', getDefaultConfig());
}

// 生成随机刷新间隔
function getRandomRefreshInterval(config) {
    var min = config.refreshIntervalMin;
    var max = config.refreshIntervalMax;
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 检查订单是否符合条件
function checkOrderConditions(order, config) {
    // 检查大厅订单是否开启
    if (!config.hallOrderEnabled) {
        console.log('大厅订单功能已关闭');
        return false;
    }
    
    // 检查订单类型
    if (!config.orderTypes.includes(order.type)) {
        console.log('订单类型不匹配：' + order.type);
        return false;
    }
    
    // 检查订单金额
    if (config.amountEnabled) {
        if (order.amount < config.minAmount || order.amount > config.maxAmount) {
            console.log('订单金额不符合条件：' + order.amount + '，要求：' + config.minAmount + '-' + config.maxAmount);
            return false;
        }
    }
    
    // 检查起点距离
    if (config.startDistanceEnabled) {
        if (order.startDistance > config.maxStartDistance) {
            console.log('起点距离超出限制：' + order.startDistance + '公里，最大允许：' + config.maxStartDistance + '公里');
            return false;
        }
    }
    
    // 检查全程距离
    if (config.totalDistanceEnabled) {
        if (order.totalDistance < config.minTotalDistance) {
            console.log('全程距离不足：' + order.totalDistance + '公里，最小要求：' + config.minTotalDistance + '公里');
            return false;
        }
    }
    
    // 检查单价
    if (config.unitPriceEnabled) {
        if (order.unitPrice < config.minUnitPrice) {
            console.log('单价过低：' + order.unitPrice + '元/公里，最低要求：' + config.minUnitPrice + '元/公里');
            return false;
        }
    }
    
    // 检查是否为大单必抢
    if (config.bigOrderEnabled) {
        if (order.amount >= config.bigOrderAmount) {
            console.log('发现大单，必须抢单：' + order.amount + '元');
            return true; // 大单必抢，直接返回true
        }
    }
    
    console.log('订单符合所有条件');
    return true;
}

// 模拟订单数据
function createMockOrder() {
    var orderTypes = ['快车', '特惠快车', '滴滴特快', '自选单', '随心接实时单', '所有订单'];
    return {
        id: 'ORDER_' + Date.now(),
        type: orderTypes[Math.floor(Math.random() * orderTypes.length)],
        amount: Math.floor(Math.random() * 200) + 10, // 10-210元
        startDistance: Math.random() * 20, // 0-20公里
        totalDistance: Math.random() * 50 + 5, // 5-55公里
        unitPrice: Math.random() * 3 + 1, // 1-4元/公里
        timestamp: new Date().toLocaleString()
    };
}

// 主业务逻辑
function processOrders() {
    var config = getConfig();
    
    console.log('当前配置：');
    console.log(JSON.stringify(config, null, 2));
    console.log('-------------------');
    
    // 模拟处理10个订单
    for (var i = 0; i < 10; i++) {
        var order = createMockOrder();
        
        console.log('处理订单 ' + (i + 1) + '：');
        console.log('订单ID：' + order.id);
        console.log('订单类型：' + order.type);
        console.log('订单金额：' + order.amount + '元');
        console.log('起点距离：' + order.startDistance.toFixed(1) + '公里');
        console.log('全程距离：' + order.totalDistance.toFixed(1) + '公里');
        console.log('单价：' + order.unitPrice.toFixed(2) + '元/公里');
        
        var shouldAccept = checkOrderConditions(order, config);
        
        if (shouldAccept) {
            console.log('✅ 接受订单');
        } else {
            console.log('❌ 拒绝订单');
        }
        
        console.log('-------------------');
        
        // 模拟刷新间隔
        var interval = getRandomRefreshInterval(config);
        console.log('等待 ' + interval + ' 毫秒后处理下一个订单...');
        sleep(interval);
    }
}

// 运行示例
console.log('开始处理大厅订单...');
processOrders();
console.log('订单处理完成！');

// 导出函数供其他脚本使用
module.exports = {
    getConfig: getConfig,
    checkOrderConditions: checkOrderConditions,
    getRandomRefreshInterval: getRandomRefreshInterval
};
