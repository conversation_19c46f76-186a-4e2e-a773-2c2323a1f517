/**
 * @version 20250819
 * 滴滴车主自动抢单脚本
 * 作者: @JiuFeng
*/
// by 九凤

const DEFAULT_TIMEOUT = 5000;    // 默认查找超时时间 (ms)
const SHORT_TIMEOUT = 1000;        // 较短超时时间
const APP_PACKAGE_NAME = "com.sdu.didi.gsui"; // 滴滴车主包名

sleep(1000);
main();

function main(){
    const startMsg = "即将执行脚本，请勿操作手机";
    log(startMsg);
    toast(startMsg);
    home();
    sleep(1000);
    const packageName = app.getPackageName("滴滴车主");
    restartApp(packageName, true)
    // app.launch(packageName);
    // 弹窗
    // closeDialog();
    sleep(1000)
    clickWidgetByPosition(text("全部工具").untilFindOne());
    sleep(1000);
    clickWidgetByPosition(text("抢单大厅").untilFindOne());
    // sleep(1000);
    // clickWidgetByPosition(text("综合排序").untilFindOne());

    for(i=0;i<10;i++){
        swipe(device.width / 2, device.height * 0.8, device.width / 2, device.height * 0.2, 500);
        toast("刷新"+i);
        sleep(1000);
    }
    killApp(packageName);

}

// 基于控件坐标点击
function clickWidgetByPosition(widget) {
  while (!widget.clickable()) {
    widget = widget.parent();
  }
  const diff = random(1, 10);
  click(widget.bounds().centerX() + diff, widget.bounds().centerY() + diff);
  sleep(500, 100);
}

function closeDialog() {
    var image_view_close = id("com.sdu.didi.gsui:id/main_dialog_banner_image_close").findOne(2000);
    if(image_view_close){
        image_view_close.click();
    }
    var image_view_close = id("com.sdu.didi.gsui:id/main_dialog_banner_close").findOne(1000);
    if(image_view_close){
        image_view_close.click();
    }
}


/**
 * 强制停止应用
 * @param {string} packageName - 应用包名
 */
function killApp(packageName) {
    log(`尝试结束应用: ${packageName}`);
    try {
        app.openAppSetting(packageName);
        sleep(1500); // 等待设置页面加载
        // 查找“结束运行”或“强制停止”按钮
        let stopButton = textMatches(/(结束运行|强行停止|FORCE STOP|Force stop)/).findOne(DEFAULT_TIMEOUT);
        if (stopButton && stopButton.enabled()) {
            if (click(stopButton)) {
                let ensureButton = textMatches(/(确定|OK)/).findOne(2000);
                if (ensureButton) {
                    click(ensureButton);
                    log("结束滴滴车主");
                } else {
                    log("程序未运行");
                }
            } else {
                log("无法点击 '结束运行/强行停止' 按钮");
            }
        } else {
            log("未找到或无法点击结束按钮，可能应用未运行");
        }
    } catch (e) {
        console.error(`结束应用 ${packageName} 时出错: ${e}`);
    }
    sleep(1000); // 等待结束动画
}

/**
 * 重启应用
 * @param {boolean} firstOpen - 是否是首次启动
 */
function restartApp(packageName, firstOpen = false) {
    killApp(packageName);
    log("启动滴滴车主应用");
    if (app.launch(packageName)) {
         // 等待应用启动加载完成，检查首页特征元素
         waitFor(() => desc('签到').findOne(DEFAULT_TIMEOUT), 2, 2000, "应用首页加载");
    } else {
        log("启动应用失败");
    }
    // 处理可能的权限请求弹窗
    let allowBtn = textMatches(/(允许|允许使用|Allow)/).findOne(1000);
    if (allowBtn) safeClick(allowBtn);
    if (!firstOpen) {
        // 非首次启动
        let signBtn = desc("签到").findOne(SHORT_TIMEOUT);
        safeClick(signBtn, "点击 '签到' 按钮")
    }
}

/**
 * 跳过启动广告
 */
function skipAd() {
    let closeButton = descMatches(/(关闭|跳过|Skip)/).findOne(1000) ||
                      idMatches(/.*(close|skip|cancel).*/).findOne(1000); // 尝试匹配常见ID

    if (safeClick(closeButton, "跳过广告")) {
        sleep(500);
    } else {
        // log("未检测到广告或无法跳过"); 
    }
}

// ========================
// === 辅助函数区域 ===
// ========================

/**
 * 格式化日期为 YYYY-MM-DD
 * @param {Date} dateObj - 日期对象
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(dateObj) {
    let year = dateObj.getFullYear();
    let month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
    let day = dateObj.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
}