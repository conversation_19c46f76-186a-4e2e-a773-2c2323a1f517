// 打印当前界面所有控件信息
var root = className("android.widget.FrameLayout").findOne();
printLayoutHierarchy(root);

function printLayoutHierarchy(node, depth) {
    depth = depth || 0;
    var indent = new Array(depth + 1).join("  ");
    
    console.log(indent + "Class: " + node.className());
    console.log(indent + "ID: " + node.id());
    console.log(indent + "Text: " + node.text());
    console.log(indent + "Desc: " + node.desc());
    console.log(indent + "Bounds: " + node.bounds());
    console.log("---");
    
    node.children().forEach(function(child) {
        printLayoutHierarchy(child, depth + 1);
    });
}
