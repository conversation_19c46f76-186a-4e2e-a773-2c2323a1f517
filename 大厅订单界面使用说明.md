# 大厅订单界面使用说明

## 功能概述

这是一个基于AutoJS6开发的大厅订单配置界面，提供了完整的订单筛选和配置功能。用户可以通过界面设置各种订单条件，配置会自动保存到本地存储中，供业务脚本读取使用。

## 主要功能

### 1. 大厅订单开关
- 控制整个大厅订单功能的启用/禁用
- 当关闭时，所有大厅订单相关功能都会停止

### 2. 订单类型选择（多选）
支持以下订单类型的多选配置：
- 快车
- 特惠快车
- 滴滴特快
- 自选单
- 随心接实时单
- 所有订单

### 3. 订单金额筛选
- 开关控制：可启用/禁用金额筛选
- 最低金额：设置接单的最低金额
- 最高金额：设置接单的最高金额

### 4. 订单起点距离筛选
- 开关控制：可启用/禁用起点距离筛选
- 距离限制：设置起点距离小于指定公里数的订单

### 5. 订单全程距离筛选
- 开关控制：可启用/禁用全程距离筛选
- 距离要求：设置全程距离大于指定公里数的订单

### 6. 订单单价筛选
- 开关控制：可启用/禁用单价筛选
- 最低单价：设置每公里最低价格要求

### 7. 大单必抢功能
- 开关控制：可启用/禁用大单必抢
- 大单金额：设置触发必抢的订单金额阈值

### 8. 刷新间隔设置
- 最小值：设置刷新间隔的最小毫秒数（默认500）
- 最大值：设置刷新间隔的最大毫秒数（默认1000）
- 实际刷新间隔会在最小值和最大值之间随机选择

## 文件说明

### 大厅订单界面.js
主界面文件，包含：
- 完整的UI布局定义
- 配置加载和保存功能
- 输入验证和错误处理
- 事件绑定和交互逻辑

### 业务处理示例.js
示例脚本，演示如何：
- 读取保存的配置
- 根据配置筛选订单
- 处理订单业务逻辑
- 使用随机刷新间隔

## 使用方法

### 1. 运行界面
```javascript
// 直接运行大厅订单界面.js文件
engines.execScriptFile("./大厅订单界面.js");
```

### 2. 在业务脚本中读取配置
```javascript
// 创建存储实例
var storage = storages.create('hall_order_config');

// 读取配置
var config = storage.get('config', defaultConfig);

// 使用配置进行业务处理
if (config.hallOrderEnabled) {
    // 处理大厅订单逻辑
}
```

### 3. 配置数据结构
```javascript
{
    hallOrderEnabled: true,           // 大厅订单开关
    orderTypes: ['快车', '特惠快车'],   // 选中的订单类型
    amountEnabled: false,             // 金额筛选开关
    minAmount: 0,                     // 最低金额
    maxAmount: 999,                   // 最高金额
    startDistanceEnabled: false,      // 起点距离开关
    maxStartDistance: 10,             // 最大起点距离
    totalDistanceEnabled: false,      // 全程距离开关
    minTotalDistance: 5,              // 最小全程距离
    unitPriceEnabled: false,          // 单价开关
    minUnitPrice: 2.0,                // 最低单价
    bigOrderEnabled: false,           // 大单必抢开关
    bigOrderAmount: 100,              // 大单金额阈值
    refreshIntervalMin: 500,          // 刷新间隔最小值
    refreshIntervalMax: 1000          // 刷新间隔最大值
}
```

## 输入验证规则

1. **金额验证**：
   - 必须为非负数
   - 最低金额不能大于最高金额

2. **距离验证**：
   - 必须为正数

3. **单价验证**：
   - 必须为正数

4. **刷新间隔验证**：
   - 必须为正整数
   - 最小值不能大于最大值

5. **订单类型验证**：
   - 至少选择一种订单类型

## 注意事项

1. 配置保存在本地存储中，应用重启后仍然有效
2. 开关关闭时，对应的输入框会自动禁用
3. 保存配置前会进行完整的输入验证
4. 大单必抢功能优先级最高，符合大单条件的订单会直接接受
5. 刷新间隔采用随机值，避免被检测到固定模式

## 扩展开发

如需扩展功能，可以：
1. 在界面中添加新的控件
2. 在配置结构中添加新的字段
3. 在验证函数中添加新的验证规则
4. 在业务处理中添加新的筛选逻辑

## 技术特点

- 使用AutoJS6的UI模块构建界面
- 采用Storage模块进行数据持久化
- 完整的输入验证和错误处理
- 模块化设计，便于维护和扩展
- 响应式界面，支持滚动查看所有选项
